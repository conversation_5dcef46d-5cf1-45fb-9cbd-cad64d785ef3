import { mutation, query } from './_generated/server';
import { v } from 'convex/values';
import { nanoid } from 'nanoid';

export const create = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    userId: v.string() // Use placeholder "user-1" for now, will integrate Clerk later
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("projects", {
      name: args.name,
      description: args.description,
      userId: args.userId,
      sharedId: nanoid(10),
      createdAt: Date.now()
    });
  }
});

export const getByUser = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("projects")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();
  }
});

export const getById = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.projectId);
  }
});

export const deleteProject = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // First, verify the project exists and belongs to the user
    const project = await ctx.db.get(args.projectId);

    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å slette dette prosjektet");
    }

    // Delete all log entries associated with this project
    const logEntries = await ctx.db
      .query("logEntries")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();

    // Delete each log entry and its associated image
    for (const entry of logEntries) {
      // Delete the image from storage if it exists
      if (entry.imageId) {
        await ctx.storage.delete(entry.imageId);
      }
      // Delete the log entry
      await ctx.db.delete(entry._id);
    }

    // Finally, delete the project itself
    await ctx.db.delete(args.projectId);

    return { success: true };
  }
});
