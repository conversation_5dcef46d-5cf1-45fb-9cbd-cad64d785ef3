import React from 'react';

interface ProjectCardProps {
  /** Project title */
  title: string;
  /** Project description */
  description: string;
  /** Project ID for navigation */
  projectId: string;
  /** Last updated timestamp */
  updatedAt: string;
  /** Click handler for card interaction */
  onClick: () => void;
  /** Additional CSS classes */
  className?: string;
  /** Animation delay for staggered animations */
  animationDelay?: string;
}

/**
 * Project card component following JobbLogg design system
 * Displays project information with gradient background and hover effects
 * 
 * @example
 * ```tsx
 * <ProjectCard
 *   title="Kjøkkenrenovering"
 *   description="Komplett renovering av kjøkken med nye skap og benkeplate"
 *   projectId="project-123"
 *   updatedAt="2025-01-26"
 *   onClick={() => navigate(`/project/project-123`)}
 * />
 * ```
 */
export const ProjectCard: React.FC<ProjectCardProps> = ({
  title,
  description,
  projectId: _projectId,
  updatedAt,
  onClick,
  className = '',
  animationDelay = '0s',
}) => {
  const handleClick = () => {
    onClick();
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      onClick();
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('nb-NO');
    } catch {
      return dateString;
    }
  };

  return (
    <div
      className={`
        bg-white rounded-xl shadow-soft border border-jobblogg-border hover:shadow-medium
        transition-all duration-300 animate-slide-up group cursor-pointer overflow-hidden
        focus-ring-enhanced interactive-press touch-feedback mobile-focus
        ${className}
      `.trim().replace(/\s+/g, ' ')}
      style={{ animationDelay }}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`Åpne prosjekt: ${title}`}
    >
      {/* Project Image Placeholder */}
      <div className="w-full h-44 bg-jobblogg-primary-soft flex items-center justify-center">
        <svg
          className="w-16 h-16 text-jobblogg-primary"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      </div>

      {/* Project Content */}
      <div className="p-6 flex-grow">
        <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-1 group-hover:text-jobblogg-primary transition-colors duration-200">
          {title}
        </h3>

        <p className="text-sm text-jobblogg-text-medium mb-4">
          {description || 'Ingen beskrivelse tilgjengelig'}
        </p>

        {/* Project Metadata */}
        <div className="flex items-center text-xs text-jobblogg-text-medium mb-4">
          <span className="bg-jobblogg-primary-soft text-jobblogg-primary px-2 py-1 rounded font-medium mr-2">
            Nytt
          </span>
          <span>Opprettet {formatDate(updatedAt)}</span>
        </div>
      </div>

      {/* Action Footer */}
      <div className="border-t border-jobblogg-border p-6 bg-jobblogg-neutral-light">
        <button
          className="w-full bg-jobblogg-primary text-white text-sm font-medium py-2.5 px-4 rounded-lg flex items-center justify-center gap-2 hover:bg-jobblogg-primary-dark focus-ring-enhanced transition-all duration-200 group/button"
          onClick={(e) => {
            e.stopPropagation();
            onClick();
          }}
          aria-label={`Åpne prosjektlogg for ${title}`}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
          <span>Åpne logg</span>
        </button>
      </div>
    </div>
  );
};

export default ProjectCard;
