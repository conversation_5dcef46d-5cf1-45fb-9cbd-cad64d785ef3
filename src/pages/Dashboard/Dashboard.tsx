import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { useUser, UserButton } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { Heading1, TextMedium, EmptyState } from '../../components/ui';

interface Project {
  _id: string;
  name: string;
  description?: string;
  createdAt: number;
}

interface StatCardProps {
  label: string;
  value: string | number;
  note?: string;
}

const Dashboard: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  const projects = useQuery(api.projects.getByUser, { userId: user?.id || "" });

  // Loading state with modern skeleton
  if (projects === undefined) {
    return (
      <div className="min-h-screen bg-jobblogg-neutral">
        <TopNavigation />
        <div className="container-content px-6 md:px-16 lg:px-24 xl:px-40 py-8">
          {/* Header Skeleton */}
          <div className="mb-8">
            <div className="skeleton h-12 w-64 mb-2"></div>
            <div className="skeleton h-6 w-40"></div>
          </div>

          {/* Stats Skeleton */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="card-stats">
                <div className="skeleton h-4 w-20 mb-3"></div>
                <div className="skeleton h-8 w-16 mb-2"></div>
                <div className="skeleton h-3 w-24"></div>
              </div>
            ))}
          </div>

          {/* Projects Grid Skeleton */}
          <div className="mb-6">
            <div className="skeleton h-8 w-32 mb-6"></div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="card-elevated">
                  <div className="skeleton h-48 w-full rounded-lg mb-4"></div>
                  <div className="skeleton h-6 w-3/4 mb-2"></div>
                  <div className="skeleton h-4 w-full mb-4"></div>
                  <div className="flex gap-2">
                    <div className="skeleton h-8 w-20"></div>
                    <div className="skeleton h-8 w-24"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Sort projects by creation date (newest first)
  const sortedProjects = projects.sort((a, b) => b.createdAt - a.createdAt);

  return (
    <div className="min-h-screen bg-jobblogg-neutral">
      <TopNavigation />

      <main className="px-6 md:px-16 lg:px-24 xl:px-40 py-8 bg-jobblogg-neutral">
        <div className="container-content max-w-6xl mx-auto">
          {/* Header Section */}
          <div className="mb-8">
            <Heading1>Dine prosjekter</Heading1>
            <TextMedium>Velkommen, {user?.firstName || 'Emma'}</TextMedium>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <StatCard
              label="Totalt antall prosjekter"
              value={sortedProjects.length}
            />
            <StatCard
              label="Prosjekter denne måneden"
              value={sortedProjects.filter(p => {
                const projectDate = new Date(p.createdAt);
                const now = new Date();
                return projectDate.getMonth() === now.getMonth() &&
                       projectDate.getFullYear() === now.getFullYear();
              }).length}
            />
            <StatCard
              label="Siste prosjekt"
              value={sortedProjects.length > 0 ?
                new Date(sortedProjects[0].createdAt).toLocaleDateString('nb-NO', { day: 'numeric', month: 'short' }) :
                '-'
              }
            />
            <StatCard
              label="Totalt antall bilder"
              value={0}
              note="Kommer snart"
            />
          </div>

          {/* Projects Section */}
          <div className="mb-6">
            <h2 className="text-heading-2 mb-6">Prosjekter</h2>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {sortedProjects.map((project: Project, index: number) => (
                <ProjectCardSimple
                  key={project._id}
                  title={project.name}
                  description={project.description || 'Dette er et testprosjekt'}
                  projectId={project._id}
                  updatedAt={new Date(project.createdAt).toLocaleDateString('nb-NO')}
                  onClick={() => navigate(`/project/${project._id}`)}
                  animationDelay={`${index * 0.1}s`}
                />
              ))}

              {/* Empty State */}
              {sortedProjects.length === 0 && (
                <div className="col-span-full">
                  <EmptyState
                    title="🚀 Kom i gang med ditt første prosjekt!"
                    description="JobbLogg hjelper deg å dokumentere arbeidsprosessen din med bilder og notater. Opprett ditt første prosjekt for å komme i gang."
                    actionLabel="Opprett ditt første prosjekt"
                    onAction={() => navigate('/create')}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

// Top Navigation Component
const TopNavigation: React.FC = () => {
  const { user } = useUser();

  return (
    <header className="bg-white border-b border-jobblogg-border px-6 md:px-10 py-4">
      <div className="flex items-center justify-between">
        {/* Logo and Title */}
        <div className="flex items-center gap-4">
          <div className="w-8 h-8 bg-jobblogg-primary rounded flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
            </svg>
          </div>
          <h2 className="text-xl font-bold text-strong">Prosjektdashbord</h2>
        </div>

        {/* Navigation and User */}
        <div className="flex items-center gap-6">
          <nav className="hidden md:flex items-center gap-6">
            <a href="#" className="text-small text-muted hover:text-jobblogg-primary transition-colors">Oversikt</a>
            <a href="#" className="text-small text-jobblogg-primary font-medium border-b-2 border-jobblogg-primary pb-1">Prosjekter</a>
            <a href="#" className="text-small text-muted hover:text-jobblogg-primary transition-colors">Bilder</a>
            <a href="#" className="text-small text-muted hover:text-jobblogg-primary transition-colors">Videoer</a>
            <a href="#" className="text-small text-muted hover:text-jobblogg-primary transition-colors">Dokumenter</a>
          </nav>

          <button className="w-10 h-10 rounded-full bg-jobblogg-neutral hover:bg-jobblogg-neutral-secondary transition-colors flex items-center justify-center">
            <svg className="w-5 h-5 text-muted" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
            </svg>
          </button>

          <UserButton
            afterSignOutUrl="/"
            appearance={{
              elements: {
                avatarBox: "w-10 h-10 rounded-full ring-2 ring-jobblogg-primary/20 hover:ring-jobblogg-primary/40 transition-all duration-200"
              }
            }}
          />
        </div>
      </div>
    </header>
  );
};

// Simplified Stat Card Component
const StatCard: React.FC<StatCardProps> = ({ label, value, note }) => {
  return (
    <div className="card-stats">
      <p className="text-small text-muted font-medium mb-2">{label}</p>
      <p className="text-3xl font-bold text-strong">{value}</p>
      {note && <p className="text-caption text-muted font-medium mt-1">{note}</p>}
    </div>
  );
};

// Simplified Project Card Component
interface ProjectCardSimpleProps {
  title: string;
  description: string;
  projectId: string;
  updatedAt: string;
  onClick: () => void;
  animationDelay?: string;
}

const ProjectCardSimple: React.FC<ProjectCardSimpleProps> = ({
  title,
  description,
  updatedAt,
  onClick,
  animationDelay = '0s',
}) => {
  return (
    <div
      className="card-elevated hover-lift animate-slide-up group cursor-pointer"
      style={{ animationDelay }}
      onClick={onClick}
    >
      {/* Placeholder Image */}
      <div className="w-full h-48 bg-gradient-to-br from-jobblogg-neutral to-jobblogg-neutral-secondary rounded-lg mb-4 flex items-center justify-center">
        <div className="w-24 h-24 bg-white rounded-lg shadow-soft flex items-center justify-center">
          <svg className="w-12 h-12 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
      </div>

      {/* Content */}
      <h3 className="text-lg font-semibold text-strong mb-1 group-hover:text-jobblogg-primary transition-colors">
        {title}
      </h3>
      <p className="text-small text-medium mb-2 line-clamp-2">
        {description}
      </p>
      <p className="text-caption text-muted mb-4">
        Opprettet: {updatedAt}
      </p>

      {/* Action Buttons */}
      <div className="flex gap-3">
        <button
          className="btn-primary-solid flex-1 text-sm"
          onClick={(e) => {
            e.stopPropagation();
            onClick();
          }}
        >
          Se detaljer
        </button>
        <button
          className="btn-outline flex-1 text-sm"
          onClick={(e) => {
            e.stopPropagation();
            // Handle log action
          }}
        >
          Åpne logg
        </button>
      </div>
    </div>
  );
};

export default Dashboard;
