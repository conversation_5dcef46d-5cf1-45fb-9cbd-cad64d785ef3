import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { useUser, UserButton } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { ProjectCard, Heading2, EmptyState, DashboardLayout, StatsCard, PrimaryButton } from '../../components/ui';

const Dashboard: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  const projects = useQuery(api.projects.getByUser, { userId: user?.id || "" });

  // Loading state with modern skeleton
  if (projects === undefined) {
    return (
      <div className="min-h-screen bg-jobblogg-neutral">
        <div className="container mx-auto px-4 py-8 max-w-7xl">
          {/* Header Skeleton */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12">
            <div className="flex items-center gap-6">
              <div className="skeleton h-12 w-64"></div>
              <div className="skeleton h-10 w-10 rounded-full"></div>
            </div>
            <div className="skeleton h-12 w-40 rounded-xl"></div>
          </div>

          {/* Stats Skeleton */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl p-6 shadow-lg border border-jobblogg-border">
                <div className="skeleton h-4 w-20 mb-3"></div>
                <div className="skeleton h-8 w-16 mb-2"></div>
                <div className="skeleton h-3 w-24"></div>
              </div>
            ))}
          </div>

          {/* Projects Grid Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-lg p-6 border border-jobblogg-border">
                <div className="skeleton h-48 w-full rounded-lg mb-4"></div>
                <div className="skeleton h-6 w-3/4 mb-2"></div>
                <div className="skeleton h-4 w-full mb-4"></div>
                <div className="flex gap-2">
                  <div className="skeleton h-8 w-20"></div>
                  <div className="skeleton h-8 w-24"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Sort projects by creation date (newest first)
  const sortedProjects = projects.sort((a, b) => b.createdAt - a.createdAt);

  return (
    <div className="min-h-screen bg-jobblogg-neutral">
      {/* Custom Header */}
      <header className="px-6 py-6 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-jobblogg-primary mb-2">
            Dine prosjekter
          </h1>
          <div className="flex items-center gap-4">
            <p className="text-jobblogg-text-medium">
              Velkommen tilbake, {user?.firstName || 'Bruker'}! 👋
            </p>
            <div className="flex items-center gap-2 text-sm font-medium text-jobblogg-success">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              <span>Online</span>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <UserButton
            afterSignOutUrl="/"
            appearance={{
              elements: {
                avatarBox: "w-8 h-8 rounded-full bg-jobblogg-primary text-white flex items-center justify-center font-medium"
              }
            }}
          />
          <Link to="/create">
            <button className="bg-jobblogg-primary text-white px-4 py-3 rounded-lg font-medium flex items-center gap-2 hover:bg-jobblogg-primary-dark transition-colors">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Nytt prosjekt
            </button>
          </Link>
        </div>
      </header>
      {/* Stats Section */}
      <section className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 px-6 pb-8">
        {/* Total Projects */}
        <div className="bg-white rounded-xl p-6 shadow-soft flex items-center">
          <div className="p-3 bg-jobblogg-primary-soft rounded-lg mr-4">
            <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </div>
          <div>
            <p className="text-2xl font-semibold text-jobblogg-text-strong">{sortedProjects.length}</p>
            <p className="text-sm text-jobblogg-text-medium">Totale prosjekter</p>
          </div>
        </div>

        {/* This Month */}
        <div className="bg-white rounded-xl p-6 shadow-soft flex items-center">
          <div className="p-3 bg-jobblogg-success-soft rounded-lg mr-4">
            <svg className="w-6 h-6 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <p className="text-2xl font-semibold text-jobblogg-text-strong">
              {sortedProjects.filter(p => {
                const projectDate = new Date(p.createdAt);
                const now = new Date();
                return projectDate.getMonth() === now.getMonth() &&
                       projectDate.getFullYear() === now.getFullYear();
              }).length}
            </p>
            <p className="text-sm text-jobblogg-text-medium">Denne måneden</p>
          </div>
        </div>

        {/* Last Project */}
        <div className="bg-white rounded-xl p-6 shadow-soft flex items-center">
          <div className="p-3 bg-jobblogg-highlight-soft rounded-lg mr-4">
            <svg className="w-6 h-6 text-jobblogg-highlight" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <p className="text-2xl font-semibold text-jobblogg-text-strong">
              {sortedProjects.length > 0 ?
                new Date(sortedProjects[0].createdAt).toLocaleDateString('nb-NO', { day: 'numeric', month: 'short' }) :
                '-'
              }
            </p>
            <p className="text-sm text-jobblogg-text-medium">Siste prosjekt</p>
          </div>
        </div>

        {/* Total Images */}
        <div className="bg-white rounded-xl p-6 shadow-soft flex items-center">
          <div className="p-3 bg-jobblogg-warning-soft rounded-lg mr-4">
            <svg className="w-6 h-6 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <p className="text-2xl font-semibold text-jobblogg-text-strong">0</p>
            <p className="text-sm text-jobblogg-text-medium">Totalt bilder</p>
            <p className="text-xs text-jobblogg-text-muted">Kommer snart</p>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section>
        <div className="px-6 py-6 flex items-center">
          <svg className="w-8 h-8 text-jobblogg-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          <h2 className="text-xl font-semibold text-jobblogg-text-strong">Prosjektoversikt</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 px-6 pb-8">
          {/* Project Cards */}
          {sortedProjects.map((project, index) => (
            <ProjectCard
              key={project._id}
              title={project.name}
              description={project.description || 'Ingen beskrivelse tilgjengelig'}
              projectId={project._id}
              updatedAt={new Date(project.createdAt).toLocaleDateString('nb-NO')}
              onClick={() => navigate(`/project/${project._id}`)}
              animationDelay={`${index * 0.1}s`}
            />
          ))}

          {/* Empty State */}
          {sortedProjects.length === 0 && (
            <div className="col-span-full">
              <EmptyState
                title="🚀 Kom i gang med ditt første prosjekt!"
                description="JobbLogg hjelper deg å dokumentere arbeidsprosessen din med bilder og notater. Opprett ditt første prosjekt for å komme i gang."
                actionLabel="Opprett ditt første prosjekt"
                onAction={() => navigate('/create')}
              />
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default Dashboard;
