import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { useUser, UserButton } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { ProjectCard, Heading2, EmptyState, DashboardLayout, StatsCard, PrimaryButton } from '../../components/ui';

const Dashboard: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  const projects = useQuery(api.projects.getByUser, { userId: user?.id || "" });

  // FAB tooltip and pulse state
  const [showTooltip, setShowTooltip] = useState(false);
  const [isNewUser, setIsNewUser] = useState(false);

  // Check if user is new (for pulsing animation) - only when projects exist
  useEffect(() => {
    const hasSeenFAB = localStorage.getItem('jobblogg-fab-seen');
    if (!hasSeenFAB && sortedProjects.length > 0) {
      setIsNewUser(true);
      // Mark as seen after 10 seconds or when clicked
      const timer = setTimeout(() => {
        setIsNewUser(false);
        localStorage.setItem('jobblogg-fab-seen', 'true');
      }, 10000);

      return () => clearTimeout(timer);
    }
  }, [sortedProjects.length]);

  const handleFABClick = () => {
    if (isNewUser) {
      setIsNewUser(false);
      localStorage.setItem('jobblogg-fab-seen', 'true');
    }
  };

  // Loading state with modern skeleton
  if (projects === undefined) {
    return (
      <div className="min-h-screen bg-jobblogg-neutral">
        <div className="container mx-auto px-4 py-8 max-w-7xl">
          {/* Header Skeleton */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12">
            <div className="flex items-center gap-6">
              <div className="skeleton h-12 w-64"></div>
              <div className="skeleton h-10 w-10 rounded-full"></div>
            </div>
            <div className="skeleton h-12 w-40 rounded-xl"></div>
          </div>

          {/* Stats Skeleton */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl p-6 shadow-lg border border-jobblogg-border">
                <div className="skeleton h-4 w-20 mb-3"></div>
                <div className="skeleton h-8 w-16 mb-2"></div>
                <div className="skeleton h-3 w-24"></div>
              </div>
            ))}
          </div>

          {/* Projects Grid Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-lg p-6 border border-jobblogg-border">
                <div className="skeleton h-48 w-full rounded-lg mb-4"></div>
                <div className="skeleton h-6 w-3/4 mb-2"></div>
                <div className="skeleton h-4 w-full mb-4"></div>
                <div className="flex gap-2">
                  <div className="skeleton h-8 w-20"></div>
                  <div className="skeleton h-8 w-24"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Sort projects by creation date (newest first)
  const sortedProjects = projects.sort((a, b) => b.createdAt - a.createdAt);

  return (
    <>
      <DashboardLayout
      title="Dine prosjekter"
      subtitle={`Velkommen tilbake, ${user?.firstName || 'Bruker'}! 👋`}
      headerActions={
        <div className="flex items-center gap-2">
          {/* User Identity Group: Avatar + Online Status */}
          <div className="flex items-center gap-2">
            <UserButton
              afterSignOutUrl="/"
              appearance={{
                elements: {
                  avatarBox: "w-10 h-10 rounded-full ring-2 ring-jobblogg-primary/20 hover:ring-jobblogg-primary/40 transition-all duration-200"
                }
              }}
            />
            {/* Online Status Indicator */}
            <div
              className="flex items-center gap-1.5 text-jobblogg-accent text-sm font-medium"
              aria-label="Online"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                />
              </svg>
              <span>Online</span>
              <span className="sr-only">Online</span>
            </div>
          </div>
        </div>
      }
      statsSection={
        <div className="grid-stats">
          <StatsCard
            title="Totale prosjekter"
            value={sortedProjects.length}
            variant="primary"
            animationDelay="0s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            }
          />
          <StatsCard
            title="Denne måneden"
            value={sortedProjects.filter(p => {
              const projectDate = new Date(p.createdAt);
              const now = new Date();
              return projectDate.getMonth() === now.getMonth() &&
                     projectDate.getFullYear() === now.getFullYear();
            }).length}
            variant="accent"
            animationDelay="0.1s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            }
          />
          <StatsCard
            title="Siste prosjekt"
            value={sortedProjects.length > 0 ?
              new Date(sortedProjects[0].createdAt).toLocaleDateString('nb-NO', { day: 'numeric', month: 'short' }) :
              '-'
            }
            variant="primary"
            animationDelay="0.2s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
          />
          <StatsCard
            title="Totalt bilder"
            value={0}
            subtitle="Kommer snart"
            variant="warning"
            animationDelay="0.3s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            }
          />
        </div>
      }
    >

      {/* Projects Grid Section */}
      <section className="space-section">
        <Heading2 className="mb-6 flex items-center gap-3">
          <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          Prosjektoversikt
        </Heading2>

        <div className="grid-mobile-cards">
          {/* Project Cards using UI Component */}
          {sortedProjects.map((project, index) => (
            <ProjectCard
              key={project._id}
              title={project.name}
              description={project.description || 'Ingen beskrivelse tilgjengelig'}
              projectId={project._id}
              updatedAt={new Date(project.createdAt).toLocaleDateString('nb-NO')}
              onClick={() => navigate(`/project/${project._id}`)}
              animationDelay={`${index * 0.1}s`}
            />
          ))}

          {/* Empty State using UI Component */}
          {sortedProjects.length === 0 && (
            <div className="col-span-full">
              <EmptyState
                title="🚀 Kom i gang med ditt første prosjekt!"
                description="JobbLogg hjelper deg å dokumentere arbeidsprosessen din med bilder og notater. Opprett ditt første prosjekt for å komme i gang."
                actionLabel="Opprett ditt første prosjekt"
                onAction={() => navigate('/create')}
              />
            </div>
          )}
        </div>
      </section>
      </DashboardLayout>

      {/* Floating Action Button with Tooltip - Only show when projects exist */}
      {sortedProjects.length > 0 && (
        <div className="fixed bottom-6 right-6 z-50">
        {/* Tooltip */}
        {showTooltip && (
          <div className="
            absolute bottom-16 right-0 mb-2
            bg-jobblogg-text-strong text-white text-sm font-medium
            px-3 py-2 rounded-lg shadow-lg
            whitespace-nowrap
            animate-fade-in
            before:content-[''] before:absolute before:top-full before:right-4
            before:border-4 before:border-transparent before:border-t-jobblogg-text-strong
          ">
            Nytt prosjekt
          </div>
        )}

        <Link to="/create" onClick={handleFABClick}>
          <button
            className={`
              relative w-14 h-14 bg-jobblogg-primary hover:bg-jobblogg-primary-dark
              rounded-full shadow-lg hover:shadow-xl
              flex items-center justify-center
              transition-all duration-200 ease-in-out
              hover:scale-110 active:scale-95
              focus:outline-none focus:ring-4 focus:ring-jobblogg-primary/30
              group
              ${isNewUser && sortedProjects.length > 0 ? 'animate-pulse' : ''}
            `}
            aria-label="Opprett nytt prosjekt"
            onMouseEnter={() => setShowTooltip(true)}
            onMouseLeave={() => setShowTooltip(false)}
            onFocus={() => setShowTooltip(true)}
            onBlur={() => setShowTooltip(false)}
          >
            {/* Pulsing ring for new users with projects */}
            {isNewUser && sortedProjects.length > 0 && (
              <div className="
                absolute inset-0 rounded-full
                bg-jobblogg-primary opacity-30
                animate-ping
              " />
            )}

            <svg
              className="w-6 h-6 text-white group-hover:rotate-90 transition-transform duration-200 relative z-10"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4v16m8-8H4"
              />
            </svg>
          </button>
        </Link>
        </div>
      )}
    </>
  );
};

export default Dashboard;
