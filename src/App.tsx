import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { SignedIn, SignedOut } from '@clerk/clerk-react'
import { PWAInstallBanner } from './components/PWAInstallBanner'
import { OfflineSync, StorageUsage } from './components/OfflineSync'
import {
  LazyDashboard,
  LazyCreateProject,
  LazyProjectLog,
  LazyProjectDetail,
  LazySignIn,
  LazySignUp,
  LazyPageWrapper,
  preloadCriticalRoutes
} from './components/LazyComponents'
import { useEffect } from 'react'

function App() {
  // Preload critical routes on app initialization
  useEffect(() => {
    preloadCriticalRoutes();
  }, []);

  return (
    <BrowserRouter>
      <div className="min-h-screen bg-white transition-colors duration-300">
        {/* PWA Components */}
        <PWAInstallBanner />
        <OfflineSync />
        <StorageUsage />
        <Routes>
          {/* Authentication Routes - Accessible to unauthenticated users */}
          <Route path="/sign-in" element={
            <LazyPageWrapper>
              <LazySignIn />
            </LazyPageWrapper>
          } />
          <Route path="/sign-up" element={
            <LazyPageWrapper>
              <LazySignUp />
            </LazyPageWrapper>
          } />

          {/* Protected Routes - Require authentication */}
          <Route path="/" element={
            <>
              <SignedIn>
                <LazyPageWrapper>
                  <LazyDashboard />
                </LazyPageWrapper>
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />
          <Route path="/create" element={
            <>
              <SignedIn>
                <LazyPageWrapper>
                  <LazyCreateProject />
                </LazyPageWrapper>
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />
          <Route path="/project/:projectId/details" element={
            <>
              <SignedIn>
                <LazyPageWrapper>
                  <LazyProjectDetail />
                </LazyPageWrapper>
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />
          <Route path="/project/:projectId" element={
            <>
              <SignedIn>
                <LazyPageWrapper>
                  <LazyProjectLog />
                </LazyPageWrapper>
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />
        </Routes>
      </div>
    </BrowserRouter>
  )
}

export default App
